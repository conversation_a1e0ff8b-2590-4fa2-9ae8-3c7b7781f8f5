import cv2
import argparse
import os

def detect_faces_opencv(image_path, output_path=None):
    """
    Simple face detection using OpenCV's Haar Cascade classifier.
    This doesn't do face recognition, but demonstrates face detection.
    """
    # Load the cascade classifier
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Read the image
    image = cv2.imread(image_path)
    if image is None:
        print(f"[ERROR] Cannot read image: {image_path}")
        return
    
    # Convert to grayscale for detection
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Detect faces
    faces = face_cascade.detectMultiScale(
        gray,
        scaleFactor=1.1,
        minNeighbors=5,
        minSize=(30, 30)
    )
    
    print(f"[INFO] Found {len(faces)} face(s) in the image")
    
    # Draw rectangles around faces
    for (x, y, w, h) in faces:
        cv2.rectangle(image, (x, y), (x+w, y+h), (0, 255, 0), 2)
        cv2.putText(image, "Face", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # Save or display result
    if output_path:
        cv2.imwrite(output_path, image)
        print(f"[OK] Saved result to {output_path}")
    else:
        cv2.imshow("Face Detection", image)
        print("[INFO] Press any key to close the window")
        cv2.waitKey(0)
        cv2.destroyAllWindows()

def detect_faces_webcam():
    """
    Real-time face detection from webcam using OpenCV.
    """
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("[ERROR] Could not open webcam.")
        return
    
    print("[INFO] Starting webcam face detection. Press 'q' to quit.")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("[WARN] Frame grab failed.")
            break
        
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 5)
        
        for (x, y, w, h) in faces:
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(frame, "Face", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow("Face Detection", frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

def main():
    parser = argparse.ArgumentParser(description="Simple face detection using OpenCV")
    parser.add_argument("--mode", choices=["image", "webcam"], default="webcam", 
                       help="Detection mode: image or webcam")
    parser.add_argument("--image", help="Path to input image (required for image mode)")
    parser.add_argument("--output", help="Path to save output image (optional)")
    
    args = parser.parse_args()
    
    if args.mode == "image":
        if not args.image:
            print("[ERROR] --image is required for image mode")
            return
        if not os.path.exists(args.image):
            print(f"[ERROR] Image file not found: {args.image}")
            return
        detect_faces_opencv(args.image, args.output)
    else:
        detect_faces_webcam()

if __name__ == "__main__":
    main()
