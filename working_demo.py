#!/usr/bin/env python3
"""
Working Face Recognition Demo

This demo works around camera access issues and provides multiple ways to test face recognition.
"""

import cv2
import numpy as np
import os
from pathlib import Path

def print_header():
    print("🎭 WORKING FACE RECOGNITION DEMO")
    print("=" * 50)
    print()

def diagnose_camera_issues():
    """Diagnose and suggest fixes for camera issues"""
    print("🔍 CAMERA DIAGNOSTICS")
    print("=" * 30)
    print()
    
    print("The error you're seeing indicates camera access issues.")
    print("Common causes and solutions:")
    print()
    print("❌ ISSUE: Camera access denied")
    print("✅ SOLUTION: Check Windows camera privacy settings")
    print("   - Go to Settings > Privacy & Security > Camera")
    print("   - Enable 'Allow apps to access your camera'")
    print("   - Enable 'Allow desktop apps to access your camera'")
    print()
    print("❌ ISSUE: Camera in use by another application")
    print("✅ SOLUTION: Close other apps using camera (Zoom, Teams, etc.)")
    print()
    print("❌ ISSUE: Driver issues")
    print("✅ SOLUTION: Update camera drivers in Device Manager")
    print()

def create_test_images():
    """Create realistic test face images"""
    print("🎨 Creating test images for demonstration...")
    
    os.makedirs("test_photos", exist_ok=True)
    
    # Create more realistic face-like patterns
    def create_face_image(name, variation=0):
        img = np.ones((400, 400, 3), dtype=np.uint8) * 200
        
        # Face outline
        cv2.ellipse(img, (200, 200), (120, 150), 0, 0, 360, (180, 150, 120), -1)
        
        # Eyes
        eye_y = 160 + variation
        cv2.circle(img, (160, eye_y), 15, (50, 50, 50), -1)  # Left eye
        cv2.circle(img, (240, eye_y), 15, (50, 50, 50), -1)  # Right eye
        
        # Nose
        nose_points = np.array([[200, 190], [195, 220], [205, 220]], np.int32)
        cv2.fillPoly(img, [nose_points], (160, 120, 100))
        
        # Mouth
        mouth_y = 250 + variation
        cv2.ellipse(img, (200, mouth_y), (25, 10), 0, 0, 180, (100, 50, 50), -1)
        
        # Add some noise for realism
        noise = np.random.randint(-20, 20, img.shape, dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        return img
    
    # Create different test images
    img1 = create_face_image("person1", 0)
    img2 = create_face_image("person1", 5)  # Same person, slight variation
    img3 = create_face_image("person2", 20)  # Different person
    
    cv2.imwrite("test_photos/person1_photo1.jpg", img1)
    cv2.imwrite("test_photos/person1_photo2.jpg", img2)
    cv2.imwrite("test_photos/person2_photo1.jpg", img3)
    
    print("✅ Test images created:")
    print("   test_photos/person1_photo1.jpg")
    print("   test_photos/person1_photo2.jpg")
    print("   test_photos/person2_photo1.jpg")
    print()

def compare_faces(img1_path, img2_path):
    """Compare two face images"""
    print(f"🔍 Comparing faces:")
    print(f"   📷 Image 1: {img1_path}")
    print(f"   📷 Image 2: {img2_path}")
    print()
    
    # Load images
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    if img1 is None or img2 is None:
        print("❌ Could not load one or both images")
        return
    
    # Initialize face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Detect faces
    faces1 = face_cascade.detectMultiScale(gray1, 1.1, 4, minSize=(50, 50))
    faces2 = face_cascade.detectMultiScale(gray2, 1.1, 4, minSize=(50, 50))
    
    print(f"📊 Faces detected - Image 1: {len(faces1)}, Image 2: {len(faces2)}")
    
    if len(faces1) == 0 or len(faces2) == 0:
        print("⚠️  No faces detected in one or both images")
        return
    
    # Get largest face from each image
    face1 = max(faces1, key=lambda x: x[2] * x[3])
    face2 = max(faces2, key=lambda x: x[2] * x[3])
    
    # Extract face regions
    x1, y1, w1, h1 = face1
    x2, y2, w2, h2 = face2
    
    face_roi1 = gray1[y1:y1+h1, x1:x1+w1]
    face_roi2 = gray2[y2:y2+h2, x2:x2+w2]
    
    # Resize for comparison
    face_roi1 = cv2.resize(face_roi1, (100, 100))
    face_roi2 = cv2.resize(face_roi2, (100, 100))
    
    # Calculate similarity using multiple methods
    
    # 1. Template matching
    result = cv2.matchTemplate(face_roi1, face_roi2, cv2.TM_CCOEFF_NORMED)
    template_sim = result[0][0]
    
    # 2. Histogram comparison
    hist1 = cv2.calcHist([face_roi1], [0], None, [256], [0, 256])
    hist2 = cv2.calcHist([face_roi2], [0], None, [256], [0, 256])
    hist_sim = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
    
    # 3. Structural similarity (simple version)
    diff = cv2.absdiff(face_roi1, face_roi2)
    structural_sim = 1.0 - (np.mean(diff) / 255.0)
    
    # Combined similarity score
    final_score = (template_sim + hist_sim + structural_sim) / 3
    
    print(f"📈 SIMILARITY ANALYSIS:")
    print(f"   🎯 Template Matching: {template_sim:.3f}")
    print(f"   📊 Histogram Similarity: {hist_sim:.3f}")
    print(f"   🔍 Structural Similarity: {structural_sim:.3f}")
    print(f"   ⭐ Final Score: {final_score:.3f}")
    print()
    
    # Interpretation
    if final_score > 0.8:
        print("✅ HIGH SIMILARITY - Very likely the same person")
        result_text = "SAME PERSON"
        color = (0, 255, 0)
    elif final_score > 0.6:
        print("🟡 MEDIUM SIMILARITY - Possibly the same person")
        result_text = "POSSIBLY SAME"
        color = (0, 255, 255)
    else:
        print("❌ LOW SIMILARITY - Likely different people")
        result_text = "DIFFERENT PEOPLE"
        color = (0, 0, 255)
    
    # Create visualization
    create_comparison_visual(img1, img2, face1, face2, final_score, result_text, color)

def create_comparison_visual(img1, img2, face1, face2, score, result_text, color):
    """Create a visual comparison"""
    
    # Draw face rectangles
    x1, y1, w1, h1 = face1
    x2, y2, w2, h2 = face2
    
    cv2.rectangle(img1, (x1, y1), (x1+w1, y1+h1), color, 3)
    cv2.rectangle(img2, (x2, y2), (x2+w2, y2+h2), color, 3)
    
    # Resize images to reasonable size
    img1 = cv2.resize(img1, (300, 400))
    img2 = cv2.resize(img2, (300, 400))
    
    # Create side-by-side comparison
    comparison = np.hstack([img1, img2])
    
    # Add text overlay
    cv2.putText(comparison, f"Score: {score:.3f}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(comparison, result_text, (10, 70), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
    
    # Save result
    os.makedirs("outputs", exist_ok=True)
    output_path = "outputs/face_comparison.jpg"
    cv2.imwrite(output_path, comparison)
    
    print(f"💾 Comparison saved to: {output_path}")
    print("👀 Opening comparison window... (press any key to close)")
    
    # Display result
    cv2.imshow("Face Comparison Result", comparison)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def run_demo():
    """Run the complete demo"""
    print_header()
    
    while True:
        print("Choose an option:")
        print("1. 🔍 Diagnose camera issues")
        print("2. 🎨 Create test images")
        print("3. 🆚 Compare two faces")
        print("4. 🎯 Run full comparison demo")
        print("5. ❌ Exit")
        print()
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == "1":
            diagnose_camera_issues()
        elif choice == "2":
            create_test_images()
        elif choice == "3":
            print("\n📁 Available images:")
            for img_path in Path(".").rglob("*.jpg"):
                print(f"   {img_path}")
            print()
            
            img1 = input("Enter path to first image: ").strip()
            img2 = input("Enter path to second image: ").strip()
            
            if img1 and img2:
                compare_faces(img1, img2)
            else:
                print("❌ Please provide both image paths")
        elif choice == "4":
            # Full demo
            create_test_images()
            print("\n🎯 DEMO 1: Same person comparison")
            compare_faces("test_photos/person1_photo1.jpg", "test_photos/person1_photo2.jpg")
            
            input("\nPress ENTER for next comparison...")
            
            print("\n🎯 DEMO 2: Different people comparison")
            compare_faces("test_photos/person1_photo1.jpg", "test_photos/person2_photo1.jpg")
            
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice")
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    run_demo()
