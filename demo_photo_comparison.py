#!/usr/bin/env python3
"""
Demo script for Photo-Camera Comparison

This script demonstrates how to compare reference photos with live camera feed.
"""

import os
import cv2
import numpy as np
from pathlib import Path

def create_sample_setup():
    """Create sample directory structure for testing"""
    print("🔧 Setting up demo environment...")
    
    # Create directories
    os.makedirs("reference_photos/demo_person", exist_ok=True)
    os.makedirs("outputs", exist_ok=True)
    
    print("✅ Created directory structure:")
    print("   reference_photos/demo_person/")
    print("   outputs/")
    print()

def capture_reference_photo():
    """Capture a reference photo from webcam"""
    print("📸 CAPTURE REFERENCE PHOTO")
    print("=" * 40)
    print("This will capture your photo to use as reference.")
    print("Position yourself in front of the camera and press SPACE to capture.")
    print("Press ESC to skip this step.")
    print()
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("[ERROR] Could not open webcam.")
        return False
    
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # Detect faces for guidance
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)
        
        # Draw face rectangles
        for (x, y, w, h) in faces:
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(frame, "Face detected", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Instructions
        cv2.putText(frame, "SPACE: Capture photo | ESC: Skip", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Faces detected: {len(faces)}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow("Capture Reference Photo", frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord(' '):  # Space to capture
            if len(faces) > 0:
                photo_path = "reference_photos/demo_person/reference.jpg"
                cv2.imwrite(photo_path, frame)
                print(f"✅ Reference photo saved: {photo_path}")
                cap.release()
                cv2.destroyAllWindows()
                return True
            else:
                print("⚠️  No face detected. Please position yourself in front of the camera.")
        elif key == 27:  # ESC to skip
            print("⏭️  Skipped reference photo capture.")
            cap.release()
            cv2.destroyAllWindows()
            return False
    
    cap.release()
    cv2.destroyAllWindows()
    return False

def run_comparison_demo():
    """Run the photo comparison demo"""
    print("\n🎯 PHOTO-CAMERA COMPARISON DEMO")
    print("=" * 40)
    
    # Check if we have reference photos
    ref_dir = Path("reference_photos")
    has_photos = False
    
    if ref_dir.exists():
        for person_dir in ref_dir.iterdir():
            if person_dir.is_dir():
                photos = list(person_dir.glob("*.jpg")) + list(person_dir.glob("*.png"))
                if photos:
                    has_photos = True
                    break
    
    if not has_photos:
        print("📁 No reference photos found.")
        print("Let's capture one first...")
        if not capture_reference_photo():
            print("❌ Cannot proceed without reference photos.")
            return
    
    # Run the comparison
    print("\n🚀 Starting photo-camera comparison...")
    print("The system will:")
    print("1. Load your reference photos")
    print("2. Train the recognition model")
    print("3. Start live camera comparison")
    print()
    print("Controls:")
    print("- Green box = Recognized person")
    print("- Red box = Unknown person")
    print("- Press 'q' to quit")
    print("- Press 's' to save screenshot")
    print()
    
    input("Press ENTER to start...")
    
    # Import and run the comparison system
    try:
        from photo_camera_comparison import PhotoCameraComparison
        
        comparator = PhotoCameraComparison()
        
        if comparator.load_reference_photos("reference_photos"):
            comparator.start_camera_comparison()
        else:
            print("❌ Failed to load reference photos.")
            
    except Exception as e:
        print(f"❌ Error running comparison: {e}")

def show_instructions():
    """Show detailed instructions"""
    print("📋 HOW TO USE PHOTO-CAMERA COMPARISON")
    print("=" * 50)
    print()
    print("🎯 Purpose:")
    print("   Compare reference photos with live camera to identify people")
    print()
    print("📁 Setup Options:")
    print("   1. Single photo:    python photo_camera_comparison.py --photos photo.jpg")
    print("   2. Multiple people: python photo_camera_comparison.py --photos reference_photos/")
    print()
    print("📸 Photo Tips:")
    print("   ✅ Clear, front-facing photos")
    print("   ✅ Good lighting")
    print("   ✅ Multiple photos per person (better accuracy)")
    print("   ❌ Avoid blurry, dark, or angled photos")
    print()
    print("⚙️ Advanced:")
    print("   --threshold 60   (stricter matching)")
    print("   --threshold 100  (more lenient)")
    print()

def main():
    print("🎭 PHOTO-CAMERA COMPARISON DEMO")
    print("=" * 50)
    print()
    print("This demo will help you:")
    print("1. Set up reference photos")
    print("2. Test photo-camera comparison")
    print("3. Learn how to use the system")
    print()
    
    while True:
        print("Choose an option:")
        print("1. 📸 Capture reference photo")
        print("2. 🎯 Run comparison demo")
        print("3. 📋 Show instructions")
        print("4. 🔧 Setup demo environment")
        print("5. ❌ Exit")
        print()
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == "1":
            create_sample_setup()
            capture_reference_photo()
        elif choice == "2":
            run_comparison_demo()
        elif choice == "3":
            show_instructions()
        elif choice == "4":
            create_sample_setup()
            print("✅ Demo environment ready!")
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")
        
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main()
