import cv2
import numpy as np
import argparse
import os
from pathlib import Path

def compare_two_images(image1_path, image2_path):
    """
    Simple comparison between two images using face detection and basic matching
    """
    print(f"🔍 Comparing {image1_path} with {image2_path}")
    
    # Initialize face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Read images
    img1 = cv2.imread(image1_path)
    img2 = cv2.imread(image2_path)
    
    if img1 is None:
        print(f"❌ Cannot read image: {image1_path}")
        return
    if img2 is None:
        print(f"❌ Cannot read image: {image2_path}")
        return
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Detect faces
    faces1 = face_cascade.detectMultiScale(gray1, 1.3, 5, minSize=(50, 50))
    faces2 = face_cascade.detectMultiScale(gray2, 1.3, 5, minSize=(50, 50))
    
    print(f"📊 Image 1: {len(faces1)} face(s) detected")
    print(f"📊 Image 2: {len(faces2)} face(s) detected")
    
    if len(faces1) == 0 or len(faces2) == 0:
        print("⚠️  Cannot compare - no faces detected in one or both images")
        return
    
    # Get the largest face from each image
    face1 = max(faces1, key=lambda x: x[2] * x[3])
    face2 = max(faces2, key=lambda x: x[2] * x[3])
    
    # Extract face regions
    x1, y1, w1, h1 = face1
    x2, y2, w2, h2 = face2
    
    face_roi1 = gray1[y1:y1+h1, x1:x1+w1]
    face_roi2 = gray2[y2:y2+h2, x2:x2+w2]
    
    # Resize to same size for comparison
    face_roi1 = cv2.resize(face_roi1, (200, 200))
    face_roi2 = cv2.resize(face_roi2, (200, 200))
    
    # Calculate similarity using template matching
    result = cv2.matchTemplate(face_roi1, face_roi2, cv2.TM_CCOEFF_NORMED)
    similarity = result[0][0]
    
    # Calculate histogram similarity
    hist1 = cv2.calcHist([face_roi1], [0], None, [256], [0, 256])
    hist2 = cv2.calcHist([face_roi2], [0], None, [256], [0, 256])
    hist_similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
    
    # Average the similarities
    final_similarity = (similarity + hist_similarity) / 2
    
    print(f"🎯 Template Matching Similarity: {similarity:.3f}")
    print(f"🎯 Histogram Similarity: {hist_similarity:.3f}")
    print(f"🎯 Final Similarity Score: {final_similarity:.3f}")
    
    # Interpretation
    if final_similarity > 0.7:
        print("✅ HIGH SIMILARITY - Likely the same person")
    elif final_similarity > 0.5:
        print("🟡 MEDIUM SIMILARITY - Possibly the same person")
    else:
        print("❌ LOW SIMILARITY - Likely different people")
    
    # Create comparison visualization
    create_comparison_visualization(img1, img2, face1, face2, final_similarity)

def create_comparison_visualization(img1, img2, face1, face2, similarity):
    """Create a side-by-side comparison image"""
    
    # Draw rectangles around faces
    x1, y1, w1, h1 = face1
    x2, y2, w2, h2 = face2
    
    cv2.rectangle(img1, (x1, y1), (x1+w1, y1+h1), (0, 255, 0), 3)
    cv2.rectangle(img2, (x2, y2), (x2+w2, y2+h2), (0, 255, 0), 3)
    
    # Add labels
    cv2.putText(img1, "Reference", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(img2, "Comparison", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # Resize images to same height
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    
    target_height = min(h1, h2, 600)  # Max height of 600px
    
    img1_resized = cv2.resize(img1, (int(w1 * target_height / h1), target_height))
    img2_resized = cv2.resize(img2, (int(w2 * target_height / h2), target_height))
    
    # Create side-by-side comparison
    comparison = np.hstack([img1_resized, img2_resized])
    
    # Add similarity score
    score_text = f"Similarity: {similarity:.3f}"
    cv2.putText(comparison, score_text, (10, comparison.shape[0] - 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
    
    # Save comparison image
    output_path = "outputs/comparison_result.jpg"
    os.makedirs("outputs", exist_ok=True)
    cv2.imwrite(output_path, comparison)
    print(f"💾 Comparison saved to: {output_path}")
    
    # Display the result
    cv2.imshow("Photo Comparison", comparison)
    print("👀 Press any key to close the comparison window...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def test_camera_access():
    """Test if camera is accessible"""
    print("🎥 Testing camera access...")
    
    for i in range(3):  # Try camera indices 0, 1, 2
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f"✅ Camera {i} is working!")
                cap.release()
                return i
            cap.release()
        else:
            print(f"❌ Camera {i} not accessible")
    
    print("❌ No working camera found")
    return None

def create_sample_images():
    """Create sample images for testing"""
    print("🎨 Creating sample test images...")
    
    os.makedirs("sample_images", exist_ok=True)
    
    # Create simple test patterns (not real faces, but for demo)
    img1 = np.ones((300, 300, 3), dtype=np.uint8) * 100
    cv2.rectangle(img1, (50, 50), (250, 250), (255, 255, 255), -1)
    cv2.circle(img1, (150, 120), 30, (0, 0, 0), -1)  # Eyes
    cv2.circle(img1, (150, 180), 20, (0, 0, 0), -1)  # Nose
    cv2.ellipse(img1, (150, 220), (40, 20), 0, 0, 180, (0, 0, 0), 2)  # Mouth
    
    img2 = img1.copy()
    # Slightly modify the second image
    cv2.circle(img2, (150, 120), 35, (0, 0, 0), -1)  # Slightly different eyes
    
    cv2.imwrite("sample_images/person1.jpg", img1)
    cv2.imwrite("sample_images/person2.jpg", img2)
    
    print("✅ Sample images created:")
    print("   sample_images/person1.jpg")
    print("   sample_images/person2.jpg")

def main():
    parser = argparse.ArgumentParser(description="Simple photo comparison tool")
    parser.add_argument("--image1", help="Path to first image")
    parser.add_argument("--image2", help="Path to second image")
    parser.add_argument("--test-camera", action="store_true", help="Test camera access")
    parser.add_argument("--create-samples", action="store_true", help="Create sample images")
    
    args = parser.parse_args()
    
    if args.test_camera:
        test_camera_access()
        return
    
    if args.create_samples:
        create_sample_images()
        return
    
    if not args.image1 or not args.image2:
        print("🎭 SIMPLE PHOTO COMPARISON TOOL")
        print("=" * 40)
        print()
        print("Usage examples:")
        print("  python simple_photo_comparison.py --image1 photo1.jpg --image2 photo2.jpg")
        print("  python simple_photo_comparison.py --test-camera")
        print("  python simple_photo_comparison.py --create-samples")
        print()
        
        # Interactive mode
        print("📁 Available images:")
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            for img in Path('.').glob(ext):
                print(f"   {img}")
        
        img1 = input("Enter path to first image: ").strip()
        img2 = input("Enter path to second image: ").strip()
        
        if img1 and img2:
            compare_two_images(img1, img2)
        else:
            print("❌ Both image paths are required")
        return
    
    compare_two_images(args.image1, args.image2)

if __name__ == "__main__":
    main()
