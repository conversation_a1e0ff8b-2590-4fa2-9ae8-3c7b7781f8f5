# Photo-Camera Comparison Guide

## 🎯 Purpose
Compare reference photos with live camera feed to identify people in real-time.

## 📁 Setup Reference Photos

### Option 1: Single Photo
```bash
python photo_camera_comparison.py --photos your_photo.jpg
```

### Option 2: Multiple People (Recommended)
Create this folder structure:
```
reference_photos/
├── john/
│   ├── photo1.jpg
│   ├── photo2.jpg
│   └── photo3.jpg
├── mary/
│   ├── image1.png
│   └── image2.png
└── alex/
    └── face.jpg
```

Then run:
```bash
python photo_camera_comparison.py --photos reference_photos/
```

## 🚀 How to Use

1. **Prepare Reference Photos:**
   - Put photos in `reference_photos/person_name/` folders
   - Use clear, front-facing photos
   - Multiple photos per person improve accuracy

2. **Run the Comparison:**
   ```bash
   python photo_camera_comparison.py --photos reference_photos/
   ```

3. **Live Recognition:**
   - Green boxes = Recognized person
   - Red boxes = Unknown person
   - Confidence score shown (lower = better match)

4. **Controls:**
   - Press 'q' to quit
   - Press 's' to save screenshot

## ⚙️ Advanced Options

### Adjust Recognition Sensitivity
```bash
# Stricter matching (fewer false positives)
python photo_camera_comparison.py --photos reference_photos/ --threshold 60

# More lenient matching (more detections)
python photo_camera_comparison.py --photos reference_photos/ --threshold 100
```

## 📸 Photo Tips

### Good Reference Photos:
- ✅ Clear, well-lit face
- ✅ Front-facing or slight angle
- ✅ Minimal background clutter
- ✅ Good resolution (at least 200x200 pixels)
- ✅ Multiple angles per person

### Avoid:
- ❌ Blurry or dark photos
- ❌ Extreme angles or profiles
- ❌ Sunglasses or face coverings
- ❌ Very small faces in the image

## 🔧 Troubleshooting

### "No face detected in image"
- Ensure the photo shows a clear face
- Try a different photo with better lighting
- Face should be at least 100x100 pixels

### Poor Recognition Accuracy
- Add more reference photos per person
- Use photos taken in similar lighting conditions
- Adjust the threshold parameter

### Camera Not Working
- Check if another application is using the camera
- Try changing camera index: modify `cv2.VideoCapture(0)` to `cv2.VideoCapture(1)`

## 🆚 Comparison with Full Face Recognition

| Feature | This Solution | Full dlib Solution |
|---------|---------------|-------------------|
| Setup | ✅ Easy (OpenCV only) | ❌ Complex (requires build tools) |
| Accuracy | 🟡 Good | ✅ Excellent |
| Speed | ✅ Fast | 🟡 Moderate |
| Dependencies | ✅ Minimal | ❌ Many |
| Training | 🟡 Required per session | ✅ Persistent encodings |

## 📝 Example Commands

```bash
# Basic usage with folder
python photo_camera_comparison.py --photos reference_photos/

# Single photo comparison
python photo_camera_comparison.py --photos my_photo.jpg

# Strict matching
python photo_camera_comparison.py --photos reference_photos/ --threshold 50

# Lenient matching  
python photo_camera_comparison.py --photos reference_photos/ --threshold 90
```
