import argparse
import pickle
import cv2
import face_recognition
import imutils
import numpy as np
from app.config import (
    DEFAULT_DETECTION_MODEL, DEFAULT_TOLERANCE, DEFAULT_UPSAMPLE, FRAME_RESIZE_WIDTH
)
from app.preprocess import apply_clahe_bgr

def draw_label(frame, box, name, distance):
    top, right, bottom, left = box
    cv2.rectangle(frame, (left, top), (right, bottom), (0, 255, 0), 2)
    label = f"{name} ({distance:.2f})" if name != "Unknown" else name
    y = top - 10 if top - 10 > 10 else top + 20
    cv2.putText(frame, label, (left, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

def main():
    ap = argparse.ArgumentParser(description="Real-time face recognition from webcam.")
    ap.add_argument("--encodings", default="outputs/encodings.pkl", help="Path to encodings pickle file")
    ap.add_argument("--tolerance", type=float, default=DEFAULT_TOLERANCE, help="Match tolerance (lower is stricter)")
    ap.add_argument("--model", default=DEFAULT_DETECTION_MODEL, choices=["hog","cnn"], help="Detector model")
    ap.add_argument("--upsample", type=int, default=DEFAULT_UPSAMPLE, help="Upsample factor for detection")
    ap.add_argument("--camera", type=int, default=0, help="Camera index (default 0)")
    ap.add_argument("--clahe", action="store_true", help="Apply CLAHE normalization to improve contrast")
    args = ap.parse_args()

    # Load encodings
    try:
        with open(args.encodings, "rb") as f:
            data = pickle.load(f)
        known_encodings = data["encodings"]
        known_names = data["names"]
    except Exception as e:
        print(f"[ERROR] Could not load encodings: {e}")
        return

    print(f"[INFO] Loaded {len(known_encodings)} encodings.")

    cap = cv2.VideoCapture(args.camera)
    if not cap.isOpened():
        print("[ERROR] Could not open webcam.")
        return

    while True:
        ret, frame = cap.read()
        if not ret:
            print("[WARN] Frame grab failed.")
            break

        # optional illumination normalization
        if args.clahe:
            frame = apply_clahe_bgr(frame)

        # resize for speed & convert to RGB for face_recognition
        frame_small = imutils.resize(frame, width=FRAME_RESIZE_WIDTH)
        rgb_small = cv2.cvtColor(frame_small, cv2.COLOR_BGR2RGB)

        boxes = face_recognition.face_locations(rgb_small, number_of_times_to_upsample=args.upsample, model=args.model)
        if not boxes:
            cv2.putText(frame_small, "No face detected", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0,0,255), 2)
            cv2.imshow("Face Recognition", frame_small)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
            continue

        encodings = face_recognition.face_encodings(rgb_small, boxes)
        names = []
        dists = []

        for enc in encodings:
            # compute distances to all known encodings
            distances = face_recognition.face_distance(known_encodings, enc)
            if len(distances) == 0:
                names.append("Unknown")
                dists.append(1.0)
                continue
            best_idx = int(np.argmin(distances))
            best_dist = float(distances[best_idx])
            name = known_names[best_idx] if best_dist <= args.tolerance else "Unknown"
            names.append(name)
            dists.append(best_dist)

        # draw results
        for (top, right, bottom, left), name, dist in zip(boxes, names, dists):
            draw_label(frame_small, (top, right, bottom, left), name, dist)

        cv2.imshow("Face Recognition", frame_small)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
