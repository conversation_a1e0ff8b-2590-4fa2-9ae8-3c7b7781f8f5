import os
import argparse
import cv2
import pickle
import face_recognition

def list_images(root):
    exts = {'.jpg', '.jpeg', '.png', '.bmp'}
    for dirpath, _, filenames in os.walk(root):
        for fn in filenames:
            if os.path.splitext(fn.lower())[1] in exts:
                yield os.path.join(dirpath, fn)

def main():
    ap = argparse.ArgumentParser(description="Build face encodings from a dataset folder structure.")
    ap.add_argument("--dataset", required=True, help="Path to dataset root (folders per person)")
    ap.add_argument("--encodings", required=True, help="Output pickle path")
    ap.add_argument("--model", default="hog", choices=["hog","cnn"], help="Detector model for face_recognition")
    ap.add_argument("--upsample", type=int, default=1, help="Number of times to upsample when finding faces")
    args = ap.parse_args()

    known_encodings = []
    known_names = []

    persons = sorted([d for d in os.listdir(args.dataset) if os.path.isdir(os.path.join(args.dataset, d))])
    if not persons:
        print(f"[WARN] No person folders found under {args.dataset}. Create {args.dataset}/<NAME>/image.jpg")
        return

    print(f"[INFO] Found {len(persons)} person(s): {persons}")

    total_faces = 0
    for person in persons:
        person_dir = os.path.join(args.dataset, person)
        images = list(list_images(person_dir))
        if not images:
            print(f"[WARN] No images for {person}. Skipping.")
            continue
        person_faces = 0
        for img_path in images:
            image = cv2.imread(img_path)
            if image is None:
                print(f"[WARN] Could not read {img_path}. Skipping.")
                continue
            rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            boxes = face_recognition.face_locations(rgb, number_of_times_to_upsample=args.upsample, model=args.model)
            encs = face_recognition.face_encodings(rgb, boxes)
            if not encs:
                print(f"[WARN] No face encodings extracted from {img_path}.")
                continue
            for enc in encs:
                known_encodings.append(enc)
                known_names.append(person)
                person_faces += 1
                total_faces += 1
        print(f"[INFO] {person}: encoded {person_faces} face(s) from {len(images)} image(s).")

    if total_faces == 0:
        print("[ERROR] No encodings were created. Check your images.")
        return

    data = {"encodings": known_encodings, "names": known_names}
    with open(args.encodings, "wb") as f:
        pickle.dump(data, f)
    print(f"[OK] Saved {total_faces} encodings to {args.encodings}")

if __name__ == "__main__":
    main()
