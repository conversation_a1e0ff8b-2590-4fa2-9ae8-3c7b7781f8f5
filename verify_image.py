import argparse
import pickle
import cv2
import face_recognition
import numpy as np
from app.config import DEFAULT_TOLERANCE, DEFAULT_DETECTION_MODEL
from app.preprocess import apply_clahe_bgr

def main():
    ap = argparse.ArgumentParser(description="Verify faces in a single image against known encodings.")
    ap.add_argument("--encodings", default="outputs/encodings.pkl", help="Path to encodings pickle file")
    ap.add_argument("--image", required=True, help="Path to input image to verify")
    ap.add_argument("--tolerance", type=float, default=DEFAULT_TOLERANCE, help="Match tolerance (lower is stricter)")
    ap.add_argument("--model", default=DEFAULT_DETECTION_MODEL, choices=["hog","cnn"], help="Detector model")
    ap.add_argument("--upsample", type=int, default=1, help="Upsample factor for detection")
    ap.add_argument("--clahe", action="store_true", help="Apply CLAHE normalization to improve contrast")
    args = ap.parse_args()

    try:
        with open(args.encodings, "rb") as f:
            data = pickle.load(f)
        known_encodings = data["encodings"]
        known_names = data["names"]
    except Exception as e:
        print(f"[ERROR] Could not load encodings: {e}")
        return

    image = cv2.imread(args.image)
    if image is None:
        print(f"[ERROR] Cannot read image: {args.image}")
        return

    if args.clahe:
        image = apply_clahe_bgr(image)

    rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    boxes = face_recognition.face_locations(rgb, number_of_times_to_upsample=args.upsample, model=args.model)
    encs = face_recognition.face_encodings(rgb, boxes)

    if not boxes:
        print("[INFO] No face detected in the image.")
        return

    names = []
    dists = []
    for enc in encs:
        distances = face_recognition.face_distance(known_encodings, enc)
        if len(distances) == 0:
            names.append("Unknown")
            dists.append(1.0)
            continue
        best_idx = int(np.argmin(distances))
        best_dist = float(distances[best_idx])
        name = known_names[best_idx] if best_dist <= args.tolerance else "Unknown"
        names.append(name)
        dists.append(best_dist)

    # Draw and save result
    for (top, right, bottom, left), name, dist in zip(boxes, names, dists):
        cv2.rectangle(image, (left, top), (right, bottom), (0,255,0), 2)
        label = f"{name} ({dist:.2f})" if name != "Unknown" else name
        y = top - 10 if top - 10 > 10 else top + 20
        cv2.putText(image, label, (left, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2)

    out_path = "outputs/last_verification.jpg"
    cv2.imwrite(out_path, image)
    print("[OK] Saved annotated image to", out_path)
    # also print summary
    print("Detections:")
    for name, dist in zip(names, dists):
        print(f" - {name} (distance={dist:.3f})")

if __name__ == "__main__":
    main()
