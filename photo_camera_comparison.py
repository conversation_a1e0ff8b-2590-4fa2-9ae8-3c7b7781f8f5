import cv2
import numpy as np
import argparse
import os
from pathlib import Path

class PhotoCameraComparison:
    def __init__(self):
        # Initialize face detection
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Initialize LBPH face recognizer (built into OpenCV)
        self.face_recognizer = cv2.face.LBPHFaceRecognizer_create()
        
        self.reference_faces = []
        self.reference_labels = []
        self.person_names = {}
        self.is_trained = False
        
    def extract_face_from_image(self, image_path):
        """Extract face from a reference photo"""
        print(f"[INFO] Processing reference image: {image_path}")
        
        image = cv2.imread(image_path)
        if image is None:
            print(f"[ERROR] Cannot read image: {image_path}")
            return None, None
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5, minSize=(100, 100))
        
        if len(faces) == 0:
            print(f"[WARNING] No face detected in {image_path}")
            return None, None
            
        # Use the largest face detected
        largest_face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = largest_face
        
        # Extract face region
        face_roi = gray[y:y+h, x:x+w]
        # Resize to standard size
        face_roi = cv2.resize(face_roi, (200, 200))
        
        print(f"[INFO] Face extracted from {image_path}")
        return face_roi, largest_face
        
    def load_reference_photos(self, photos_dir):
        """Load reference photos from directory structure"""
        photos_path = Path(photos_dir)
        if not photos_path.exists():
            print(f"[ERROR] Directory {photos_dir} does not exist")
            return False
            
        person_id = 0
        total_faces = 0
        
        # Support both single images and person folders
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        
        # Check if it's a single image file
        if photos_path.is_file() and photos_path.suffix.lower() in image_extensions:
            face_roi, _ = self.extract_face_from_image(str(photos_path))
            if face_roi is not None:
                self.reference_faces.append(face_roi)
                self.reference_labels.append(person_id)
                self.person_names[person_id] = photos_path.stem
                total_faces += 1
                print(f"[INFO] Loaded 1 face for {photos_path.stem}")
        else:
            # Process directory structure
            for person_dir in photos_path.iterdir():
                if person_dir.is_dir():
                    person_name = person_dir.name
                    person_faces = 0
                    
                    for img_file in person_dir.iterdir():
                        if img_file.suffix.lower() in image_extensions:
                            face_roi, _ = self.extract_face_from_image(str(img_file))
                            if face_roi is not None:
                                self.reference_faces.append(face_roi)
                                self.reference_labels.append(person_id)
                                person_faces += 1
                                total_faces += 1
                    
                    if person_faces > 0:
                        self.person_names[person_id] = person_name
                        print(f"[INFO] Loaded {person_faces} faces for {person_name}")
                        person_id += 1
                elif person_dir.suffix.lower() in image_extensions:
                    # Handle images directly in the root directory
                    face_roi, _ = self.extract_face_from_image(str(person_dir))
                    if face_roi is not None:
                        self.reference_faces.append(face_roi)
                        self.reference_labels.append(person_id)
                        self.person_names[person_id] = person_dir.stem
                        total_faces += 1
                        person_id += 1
                        print(f"[INFO] Loaded 1 face for {person_dir.stem}")
        
        if total_faces == 0:
            print("[ERROR] No faces found in reference photos")
            return False
            
        # Train the recognizer
        print(f"[INFO] Training recognizer with {total_faces} faces...")
        self.face_recognizer.train(self.reference_faces, np.array(self.reference_labels))
        self.is_trained = True
        print("[INFO] Training completed!")
        return True
        
    def recognize_face(self, face_roi):
        """Recognize a face using the trained model"""
        if not self.is_trained:
            return "Unknown", 100.0
            
        face_roi = cv2.resize(face_roi, (200, 200))
        label, confidence = self.face_recognizer.predict(face_roi)
        
        # Lower confidence means better match (distance-based)
        if confidence < 80:  # Threshold for recognition
            name = self.person_names.get(label, "Unknown")
            return name, confidence
        else:
            return "Unknown", confidence
            
    def start_camera_comparison(self):
        """Start live camera comparison"""
        if not self.is_trained:
            print("[ERROR] No reference photos loaded. Train the model first.")
            return
            
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("[ERROR] Could not open webcam.")
            return
            
        print("[INFO] Starting camera comparison. Press 'q' to quit, 's' to save screenshot.")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, 1.3, 5, minSize=(50, 50))
            
            for (x, y, w, h) in faces:
                # Extract face for recognition
                face_roi = gray[y:y+h, x:x+w]
                name, confidence = self.recognize_face(face_roi)
                
                # Draw rectangle and label
                color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)
                cv2.rectangle(frame, (x, y), (x+w, y+h), color, 2)
                
                # Display name and confidence
                label = f"{name} ({confidence:.1f})"
                cv2.putText(frame, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                
            # Show instructions
            cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                       
            cv2.imshow("Photo-Camera Comparison", frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                cv2.imwrite("screenshot.jpg", frame)
                print("[INFO] Screenshot saved as screenshot.jpg")
                
        cap.release()
        cv2.destroyAllWindows()

def main():
    parser = argparse.ArgumentParser(description="Compare reference photos with live camera feed")
    parser.add_argument("--photos", required=True, 
                       help="Path to reference photos (single image or directory with person folders)")
    parser.add_argument("--threshold", type=float, default=80.0,
                       help="Recognition threshold (lower = stricter)")
    
    args = parser.parse_args()
    
    # Check if reference photos exist
    if not os.path.exists(args.photos):
        print(f"[ERROR] Reference photos path does not exist: {args.photos}")
        return
        
    # Initialize comparison system
    comparator = PhotoCameraComparison()
    
    # Load reference photos
    if not comparator.load_reference_photos(args.photos):
        print("[ERROR] Failed to load reference photos")
        return
        
    # Start camera comparison
    comparator.start_camera_comparison()

if __name__ == "__main__":
    main()
