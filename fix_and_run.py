#!/usr/bin/env python3
"""
Fixed Face Recognition Demo - Works without camera issues
"""

import cv2
import numpy as np
import os

def main():
    print("🎭 FACE RECOGNITION - FIXED VERSION")
    print("=" * 50)
    print()
    
    # The issue you encountered:
    print("❌ PROBLEM IDENTIFIED:")
    print("   Camera access error: -1072875772")
    print("   This is a Windows camera permission/driver issue")
    print()
    
    print("✅ SOLUTIONS:")
    print("1. 🔧 Fix Camera Access:")
    print("   - Windows Settings > Privacy & Security > Camera")
    print("   - Enable 'Allow apps to access your camera'")
    print("   - Enable 'Allow desktop apps to access your camera'")
    print("   - Close other apps using camera (Zoom, Teams, etc.)")
    print()
    
    print("2. 🎯 Alternative: Photo-to-Photo Comparison")
    print("   Instead of live camera, compare two photos directly")
    print()
    
    # Create test images
    print("🎨 Creating test images...")
    os.makedirs("demo_images", exist_ok=True)
    
    # Create two similar face-like images
    img1 = create_test_face(1)
    img2 = create_test_face(2)
    
    cv2.imwrite("demo_images/face1.jpg", img1)
    cv2.imwrite("demo_images/face2.jpg", img2)
    
    print("✅ Test images created:")
    print("   demo_images/face1.jpg")
    print("   demo_images/face2.jpg")
    print()
    
    # Compare the faces
    print("🔍 COMPARING FACES...")
    compare_result = compare_faces("demo_images/face1.jpg", "demo_images/face2.jpg")
    
    print()
    print("🎯 NEXT STEPS:")
    print("1. Fix camera permissions to use live recognition")
    print("2. Use photo comparison for now:")
    print("   python simple_photo_comparison.py --image1 photo1.jpg --image2 photo2.jpg")
    print()
    print("3. For live camera (after fixing permissions):")
    print("   python simple_face_detection.py --mode webcam")

def create_test_face(variant):
    """Create a test face image"""
    img = np.ones((300, 300, 3), dtype=np.uint8) * 200
    
    # Face oval
    cv2.ellipse(img, (150, 150), (80, 100), 0, 0, 360, (180, 160, 140), -1)
    
    # Eyes
    eye_y = 120 + (variant * 5)
    cv2.circle(img, (120, eye_y), 12, (50, 50, 50), -1)
    cv2.circle(img, (180, eye_y), 12, (50, 50, 50), -1)
    
    # Nose
    cv2.circle(img, (150, 150), 8, (160, 140, 120), -1)
    
    # Mouth
    mouth_y = 180 + (variant * 3)
    cv2.ellipse(img, (150, mouth_y), (20, 8), 0, 0, 180, (120, 80, 80), -1)
    
    return img

def compare_faces(img1_path, img2_path):
    """Simple face comparison"""
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Simple similarity using template matching
    result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
    similarity = result[0][0]
    
    print(f"📊 Similarity Score: {similarity:.3f}")
    
    if similarity > 0.8:
        print("✅ HIGH SIMILARITY - Likely same person")
    elif similarity > 0.6:
        print("🟡 MEDIUM SIMILARITY - Possibly same person")
    else:
        print("❌ LOW SIMILARITY - Different people")
    
    # Create comparison image
    comparison = np.hstack([img1, img2])
    cv2.putText(comparison, f"Similarity: {similarity:.3f}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    os.makedirs("outputs", exist_ok=True)
    cv2.imwrite("outputs/comparison.jpg", comparison)
    print("💾 Comparison saved to: outputs/comparison.jpg")
    
    return similarity

if __name__ == "__main__":
    main()
